import React from 'react';
import { Contain<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON> } from 'react-bootstrap';
import { useNavigate } from 'react-router-dom';
import { FaBook, FaUsers, FaArrowRight, FaGraduationCap, FaHeart, FaGift } from 'react-icons/fa';

const Home = () => {
  const navigate = useNavigate();

  return (
    <div className="home-page">
      {/* Enhanced Hero Section */}
      <section className="hero-banner d-flex align-items-center">
        <Container>
          <Row className="align-items-center min-vh-100">
            <Col lg={6} className="hero-content">
              <div className="text-center text-lg-start">
                <h1 className="display-3 fw-bold mb-4 font-serif-primary">
                  Welcome to <span style={{ color: '#d2b48c' }}>BookNest</span>
                </h1>
                <p className="lead mb-4 font-serif-secondary" style={{ fontSize: '1.3rem', lineHeight: '1.6' }}>
                  Discover your next literary adventure in our carefully curated collection of books.
                  From timeless classics to contemporary bestsellers, find the perfect book for every mood.
                </p>
                <div className="d-flex gap-3 justify-content-center justify-content-lg-start flex-wrap mb-4">
                  <Button
                    variant="primary"
                    size="lg"
                    onClick={() => navigate('/login')}
                    className="px-5 py-3 login-button hero-cta"
                    style={{
                      background: 'linear-gradient(135deg, #d97706, #ea580c)',
                      border: 'none',
                      fontWeight: '600',
                      borderRadius: '12px'
                    }}
                  >
                    <FaUsers className="me-2" />
                    Get Started
                    <FaArrowRight className="ms-2" />
                  </Button>
                  <Button
                    variant="outline-light"
                    size="lg"
                    onClick={() => navigate('/books')}
                    className="px-5 py-3"
                    style={{
                      borderColor: '#d2b48c',
                      color: '#d2b48c',
                      fontWeight: '600',
                      borderRadius: '12px',
                      borderWidth: '2px'
                    }}
                  >
                    <FaBook className="me-2" />
                    Browse Books
                  </Button>
                </div>
                <div className="hero-stats d-flex gap-4 justify-content-center justify-content-lg-start">
                  <div className="text-center">
                    <div className="h4 fw-bold mb-0" style={{ color: '#d2b48c' }}>1000+</div>
                    <small className="text-light">Books Available</small>
                  </div>
                  <div className="text-center">
                    <div className="h4 fw-bold mb-0" style={{ color: '#d2b48c' }}>500+</div>
                    <small className="text-light">Happy Readers</small>
                  </div>
                  <div className="text-center">
                    <div className="h4 fw-bold mb-0" style={{ color: '#d2b48c' }}>50+</div>
                    <small className="text-light">Categories</small>
                  </div>
                </div>
              </div>
            </Col>
            <Col lg={6} className="d-none d-lg-block">
              <div className="hero-image-container text-center">
                <div className="floating-books position-relative">
                  <div className="book-stack" style={{
                    background: 'rgba(255,255,255,0.1)',
                    borderRadius: '20px',
                    padding: '3rem',
                    backdropFilter: 'blur(10px)'
                  }}>
                    <FaBook size={120} style={{ color: '#d2b48c', opacity: 0.8 }} />
                    <div className="mt-3">
                      <h4 className="text-light font-serif-primary">Your Literary Journey Awaits</h4>
                    </div>
                  </div>
                </div>
              </div>
            </Col>
          </Row>
        </Container>
      </section>
      {/* Features Section */}
      <section className="features-section py-5" style={{ background: 'linear-gradient(to bottom, #faf0e6, #fff8dc)' }}>
        <Container>
          <Row className="text-center mb-5">
            <Col>
              <h2 className="display-5 fw-bold mb-3 font-serif-primary" style={{ color: '#8b4513' }}>
                Why Choose BookNest?
              </h2>
              <p className="lead" style={{ color: '#4a4a4a' }}>
                Experience the perfect blend of classic charm and modern convenience
              </p>
            </Col>
          </Row>
          <Row>
            <Col md={4} className="text-center mb-4">
              <div className="feature-card p-4 h-100" style={{
                background: 'white',
                borderRadius: '16px',
                boxShadow: '0 8px 32px rgba(0,0,0,0.1)',
                border: '1px solid rgba(210, 180, 140, 0.3)'
              }}>
                <div className="feature-icon mb-3">
                  <FaGraduationCap size={48} style={{ color: '#8b4513' }} />
                </div>
                <h4 className="font-serif-secondary mb-3" style={{ color: '#8b4513' }}>Curated Collection</h4>
                <p style={{ color: '#4a4a4a' }}>
                  Handpicked books across all genres, from timeless classics to contemporary bestsellers
                </p>
              </div>
            </Col>
            <Col md={4} className="text-center mb-4">
              <div className="feature-card p-4 h-100" style={{
                background: 'white',
                borderRadius: '16px',
                boxShadow: '0 8px 32px rgba(0,0,0,0.1)',
                border: '1px solid rgba(210, 180, 140, 0.3)'
              }}>
                <div className="feature-icon mb-3">
                  <FaHeart size={48} style={{ color: '#8b4513' }} />
                </div>
                <h4 className="font-serif-secondary mb-3" style={{ color: '#8b4513' }}>Personalized Experience</h4>
                <p style={{ color: '#4a4a4a' }}>
                  Get recommendations tailored to your reading preferences and discover new favorites
                </p>
              </div>
            </Col>
            <Col md={4} className="text-center mb-4">
              <div className="feature-card p-4 h-100" style={{
                background: 'white',
                borderRadius: '16px',
                boxShadow: '0 8px 32px rgba(0,0,0,0.1)',
                border: '1px solid rgba(210, 180, 140, 0.3)'
              }}>
                <div className="feature-icon mb-3">
                  <FaGift size={48} style={{ color: '#8b4513' }} />
                </div>
                <h4 className="font-serif-secondary mb-3" style={{ color: '#8b4513' }}>Special Offers</h4>
                <p style={{ color: '#4a4a4a' }}>
                  Enjoy exclusive deals, member discounts, and special promotions on your favorite books
                </p>
              </div>
            </Col>
          </Row>
        </Container>
      </section>
      {/* Call to Action Section */}
      <section className="cta-section py-5" style={{
        background: 'linear-gradient(135deg, #2c1810 0%, #8b4513 50%, #d2b48c 100%)',
        color: 'white'
      }}>
        <Container>
          <Row className="text-center">
            <Col>
              <h2 className="display-5 fw-bold mb-4 font-serif-primary">
                Ready to Start Your Reading Journey?
              </h2>
              <p className="lead mb-4 font-serif-secondary">
                Join thousands of book lovers and discover your next favorite read today
              </p>
              <div className="d-flex gap-3 justify-content-center flex-wrap">
                <Button
                  variant="light"
                  size="lg"
                  onClick={() => navigate('/login')}
                  className="px-5 py-3"
                  style={{
                    background: 'rgba(255,255,255,0.9)',
                    color: '#8b4513',
                    fontWeight: '600',
                    borderRadius: '12px',
                    border: 'none'
                  }}
                >
                  <FaUsers className="me-2" />
                  Join BookNest
                </Button>
                <Button
                  variant="outline-light"
                  size="lg"
                  onClick={() => navigate('/books')}
                  className="px-5 py-3"
                  style={{
                    borderColor: 'rgba(255,255,255,0.8)',
                    color: 'white',
                    fontWeight: '600',
                    borderRadius: '12px',
                    borderWidth: '2px'
                  }}
                >
                  <FaBook className="me-2" />
                  Explore Collection
                </Button>
              </div>
            </Col>
          </Row>
        </Container>
      </section>
    </div>
  );
};
export default Home;

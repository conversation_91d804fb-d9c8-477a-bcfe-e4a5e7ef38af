
import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { Card } from 'react-bootstrap';
import { Link } from 'react-router-dom';
import Snavbar from './Snavbar';
import Footer from '../Components/Footer';

// BookStore Home Page (Seller Dashboard)
function Shome() {
  const [books, setBooks] = useState([]);
  const [orders, setOrders] = useState([]);
  const [bookError, setBookError] = useState(null);
  const [orderError, setOrderError] = useState(null);

  useEffect(() => {
    // Fetch books (as items) for the seller
    const user = JSON.parse(localStorage.getItem('user'));
    if (user) {
      axios
        .get(`https://booknest-backend-55yh.onrender.com/books/seller/${user.id}`)
        .then((response) => {
          setBooks(response.data);
          setBookError(null);
        })
        .catch((error) => {
          setBookError('Error fetching books.');
          console.error('Error fetching books: ', error);
        });

      // Fetch orders for the seller
      axios
        .get(`https://booknest-backend-55yh.onrender.com/orders/seller/${user.id}`)
        .then((response) => {
          setOrders(response.data);
          setOrderError(null);
        })
        .catch((error) => {
          setOrderError('Error fetching orders.');
          console.error('Error fetching orders: ', error);
        });
    }
  }, []);

  // Calculate stats
  const totalBooks = books.length;
  const totalOrders = orders.length;

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50">
      <Snavbar />

      <div className="container py-8">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-800 mb-4">
            Seller <span className="text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 to-purple-600">Dashboard</span>
          </h1>
          <p className="text-xl text-gray-600">Manage your books and track your sales</p>
        </div>

        {/* Stats Cards */}
        <div className="grid md:grid-cols-2 gap-8 mb-12">
          <Link to="/myproducts" className="group">
            <div className="bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 border border-indigo-100 hover:border-indigo-200 hover:-translate-y-2">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-2xl font-bold text-gray-800 mb-2">My Books</h3>
                  <p className="text-gray-600">Manage your inventory</p>
                </div>
                <div className="text-right">
                  <div className="text-4xl font-bold text-indigo-600">{totalBooks}</div>
                  <div className="text-sm text-gray-500">Total Books</div>
                </div>
              </div>
              <div className="mt-4 w-16 h-16 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform">
                <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
            </div>
          </Link>

          <Link to="/orders" className="group">
            <div className="bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 border border-indigo-100 hover:border-indigo-200 hover:-translate-y-2">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-2xl font-bold text-gray-800 mb-2">Orders</h3>
                  <p className="text-gray-600">Track your sales</p>
                </div>
                <div className="text-right">
                  <div className="text-4xl font-bold text-green-600">{totalOrders}</div>
                  <div className="text-sm text-gray-500">Total Orders</div>
                </div>
              </div>
              <div className="mt-4 w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-600 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform">
                <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                </svg>
              </div>
            </div>
          </Link>
        </div>

        {/* Error Messages */}
        {bookError && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg mb-6 text-center">
            {bookError}
          </div>
        )}
        {orderError && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg mb-6 text-center">
            {orderError}
          </div>
        )}

        {/* Books Section */}
        <div className="bg-white rounded-2xl shadow-lg p-8 border border-gray-100">
          <div className="flex items-center justify-between mb-8">
            <h2 className="text-3xl font-bold text-gray-800">Your Books</h2>
            <Link
              to="/addbook"
              className="bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 text-white font-semibold py-3 px-6 rounded-lg transition-all hover:-translate-y-1 shadow-lg"
            >
              Add New Book
            </Link>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {books.length === 0 && !bookError ? (
              <div className="col-span-full text-center py-16">
                <div className="text-6xl text-gray-300 mb-4">📚</div>
                <h3 className="text-2xl font-bold text-gray-600 mb-2">No books yet</h3>
                <p className="text-gray-500 mb-6">Start building your inventory by adding your first book!</p>
                <Link
                  to="/addbook"
                  className="bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 text-white font-semibold py-3 px-6 rounded-lg transition-all hover:-translate-y-1 shadow-lg"
                >
                  Add Your First Book
                </Link>
              </div>
            ) : (
              books.map((book) => (
                <div key={book._id} className="group">
                  <div className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100 hover:border-indigo-200 hover:-translate-y-1">
                    <div className="relative overflow-hidden">
                      <img
                        src={book.itemImage ? `/${book.itemImage}` : '/default_cover.svg'}
                        alt={book.title}
                        className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                      />
                    </div>

                    <div className="p-4">
                      <h3 className="text-lg font-bold text-gray-800 mb-2 group-hover:text-indigo-600 transition-colors line-clamp-2">
                        {book.title}
                      </h3>
                      <p className="text-gray-600 mb-1">by {book.author}</p>
                      <p className="text-lg font-bold text-indigo-600 mb-4">${book.price}</p>

                      <div className="flex space-x-2">
                        <Link
                          to={`/book/${book._id}`}
                          className="flex-1 bg-indigo-500 hover:bg-indigo-600 text-white font-semibold py-2 px-3 rounded-lg text-center transition-colors text-sm"
                        >
                          View
                        </Link>
                        <Link
                          to={`/editbook/${book._id}`}
                          className="flex-1 bg-gray-500 hover:bg-gray-600 text-white font-semibold py-2 px-3 rounded-lg text-center transition-colors text-sm"
                        >
                          Edit
                        </Link>
                      </div>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default Shome;

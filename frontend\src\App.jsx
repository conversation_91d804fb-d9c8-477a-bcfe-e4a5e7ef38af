import 'bootstrap/dist/css/bootstrap.min.css';
import './styles/darkAcademia.css';
import { BrowserRouter, Routes, Route } from 'react-router-dom';

// User pages
import Login from './User/Login';
import Signup from './User/Signup';
import Uhome from './User/Uhome';
import Products from './User/Products';
import Uitem from './User/Uitem';

import Myorders from './User/Myorders';
import MyCart from './User/MyCart';
import MyOrdersNew from './User/MyOrdersNew';
import OrderItem from './User/OrderItem';
import Welcome from './User/Welcome';
import Checkout from './User/Checkout';
import CheckoutSuccess from './User/CheckoutSuccess';
import BestWishes from './User/BestWishes';

// Seller pages
import Slogin from './Seller/Slogin';
import Ssignup from './Seller/Ssignup';
import Shome from './Seller/Shome';
import Addbook from './Seller/Addbook';
import Myproducts from './Seller/Myproducts';
import Book from './Seller/Book';
import Orders from './Seller/Orders';

// Admin pages
import AdminLogin from './Admin/AdminLogin';
import Asignup from './Admin/Asignup';
import Ahome from './Admin/Ahome';
import Users from './Admin/Users';
import Seller from './Admin/Seller';
import Items from './Admin/Items';

// Common components
import Home from './Components/Home';
import Unavbar from './User/Unavbar';

// Book Store Website Routing
function App() {
  return (
    <div>
      <BrowserRouter>
        <Routes>
          {/* Home */}
          <Route path="/" element={<Home />} />

          {/* User Routes */}
          <Route path="/login" element={<Login />} />
          <Route path="/signup" element={<Signup />} />
          <Route path="/uhome" element={<Uhome />} />
          <Route path="/welcome" element={<Welcome />} />
          <Route path="/uproducts" element={<Products />} />
          <Route path="/books" element={<Products />} />
          <Route path="/uitem/:id" element={<Uitem />} />

          <Route path="/myorders" element={<Myorders />} />
          <Route path="/mycart" element={<MyCart />} />
          <Route path="/checkout" element={<Checkout />} />
          <Route path="/checkout-success" element={<CheckoutSuccess />} />
          <Route path="/myorders-new" element={<MyOrdersNew />} />
          <Route path="/orderitem/:id" element={<OrderItem />} />
          <Route path="/best-wishes" element={<BestWishes />} />
          <Route path="/nav" element={<Unavbar />} />

          {/* Seller Routes */}
          <Route path="/slogin" element={<Slogin />} />
          <Route path="/ssignup" element={<Ssignup />} />
          <Route path="/shome" element={<Shome />} />
          <Route path="/addbook" element={<Addbook />} />
          <Route path="/myproducts" element={<Myproducts />} />
          <Route path="/book/:id" element={<Book />} />
          <Route path="/orders" element={<Orders />} />

          {/* Admin Routes */}
          <Route path="/alogin" element={<AdminLogin />} />
          <Route path="/asignup" element={<Asignup />} />
          <Route path="/ahome" element={<Ahome />} />
          <Route path="/users" element={<Users />} />
          <Route path="/sellers" element={<Seller />} />
          <Route path="/items" element={<Items />} />
        </Routes>
      </BrowserRouter>
    </div>
  );
}

export default App;

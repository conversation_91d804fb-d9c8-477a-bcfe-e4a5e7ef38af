import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { BarChart, Bar, XAxis, <PERSON>A<PERSON><PERSON>, <PERSON><PERSON><PERSON>, Legend } from 'recharts';
import { Card } from 'react-bootstrap';
import { Link } from 'react-router-dom';
import Anavbar from './Anavbar';

// Admin Dashboard for Book Store
function Ahome() {
  const [users, setUsers] = useState([]);
  const [vendors, setVendors] = useState([]);
  const [books, setBooks] = useState([]);
  const [orders, setOrders] = useState([]);

  useEffect(() => {
    // Fetch users
    axios.get('https://booknest-backend-55yh.onrender.com/users')
      .then(res => setUsers(res.data))
      .catch(err => console.error('Error fetching users:', err));

    // Fetch vendors (sellers)
    axios.get('https://booknest-backend-55yh.onrender.com/sellers')
      .then(res => setVendors(res.data))
      .catch(err => console.error('Error fetching vendors:', err));

    // Fetch books
    axios.get('https://booknest-backend-55yh.onrender.com/item')
      .then(res => setBooks(res.data))
      .catch(err => console.error('Error fetching books:', err));

    // Fetch orders
    axios.get('https://booknest-backend-55yh.onrender.com/orders')
      .then(res => setOrders(res.data))
      .catch(err => console.error('Error fetching orders:', err));
  }, []);

  const totalUsers = users.length;
  const totalVendors = vendors.length;
  const totalBooks = books.length;
  const totalOrders = orders.length;

  const data = [
    { name: 'Users', value: totalUsers, fill: '#2B124C' },
    { name: 'Vendors', value: totalVendors, fill: 'cyan' },
    { name: 'Books', value: totalBooks, fill: 'blue' },
    { name: 'Orders', value: totalOrders, fill: 'orange' },
  ];

  return (
    <div>
      <Anavbar />
      <h3 className="text-center" style={{ color: "" }}>Book Store Admin Dashboard</h3>
      <Card body style={{ background: "white", width: "80%", marginLeft: "10%", marginTop: "20px", height: "580px" }}>
        <div className="flex justify-around items-center p-4">
          <Link to="/users" style={{ textDecoration: "none" }}>
            <div className="w-64 h-32 bg-red-500 rounded-lg shadow-md flex flex-col justify-center items-center text-xl font-bold text-gray-800 text-center">
              USERS <br /> <br />{totalUsers}
            </div>
          </Link>
          <Link to="/sellers" style={{ textDecoration: "none" }}>
            <div className="w-64 h-32 bg-blue-500 rounded-lg shadow-md flex flex-col justify-center items-center text-xl font-bold text-gray-800 text-center">
              Vendors <br /> <br /> {totalVendors}
            </div>
          </Link>
          <Link to="/items" style={{ textDecoration: "none" }}>
            <div className="w-64 h-32 bg-green-500 rounded-lg shadow-md flex flex-col justify-center items-center text-xl font-bold text-gray-800 text-center">
              Books <br /> <br />{totalBooks}
            </div>
          </Link>
          <Link to="/orders" style={{ textDecoration: "none" }}>
            <div className="w-64 h-32 bg-yellow-500 rounded-lg shadow-md flex flex-col justify-center items-center text-xl font-bold text-gray-800 text-center">
              Orders <br /> <br />{totalOrders}
            </div>
          </Link>
        </div>
        <br />
        <br />
        <br />
        <div style={{ paddingLeft: "350px" }}>
          <BarChart width={400} height={300} data={data}>
            <XAxis dataKey="name" />
            <YAxis />
            <Tooltip />
            <Legend />
            <Bar dataKey="value" fill="#8884d8" barSize={50} />
          </BarChart>
        </div>
      </Card>
    </div>
  );
}

export default Ahome;

import React, { useEffect, useState } from 'react';

const BACKEND_URL = 'https://booknest-backend-55yh.onrender.com';

function getImageUrl(bookName) {
  // Use the book name as filename, fallback to default_cover.svg
  const fileName = `${bookName}.jpeg`;
  return `/${fileName}`;
}

const Products = () => {
  const [books, setBooks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetch(`${BACKEND_URL}/api/books`)
      .then((res) => {
        if (!res.ok) {
          throw new Error('Network response was not ok');
        }
        return res.json();
      })
      .then((data) => {
        setBooks(data.books || []);
        setLoading(false);
      })
      .catch((err) => {
        setError('Failed to load books.');
        setLoading(false);
      });
  }, []);

  const handleAddToCart = (book) => {
    let cart = JSON.parse(localStorage.getItem('cart')) || [];
    cart.push({ ...book, quantity: 1 });
    localStorage.setItem('cart', JSON.stringify(cart));
    alert(`${book.name} added to cart!`);
  };

  if (loading) return <div className="text-center py-5">Loading books...</div>;
  if (error) return <div className="text-danger text-center py-5">{error}</div>;

  return (
    <div className="container py-5">
      <h2 className="mb-4 text-center" style={{fontFamily: 'Playfair Display, serif'}}>All Books</h2>
      <div className="row g-4">
        {books.map((book) => (
          <div className="col-12 col-sm-6 col-md-4 col-lg-3" key={book._id}>
            <div className="card h-100 shadow-sm">
              <img
                src={getImageUrl(book.name)}
                alt={book.name}
                className="card-img-top"
                onError={(e) => { e.target.onerror = null; e.target.src = '/public/default_cover.svg'; }}
                style={{height: '250px', objectFit: 'cover', background: '#222'}}
              />
              <div className="card-body d-flex flex-column">
                <h5 className="card-title" style={{fontFamily: 'Playfair Display, serif'}}>{book.name}</h5>
                <p className="card-text flex-grow-1">{book.author}</p>
                <button className="btn btn-da mt-auto" onClick={() => handleAddToCart(book)}>
                  Add to Cart
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Products;

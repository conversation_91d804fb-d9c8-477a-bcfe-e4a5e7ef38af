import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { <PERSON>, Button } from 'react-bootstrap';
import { useNavigate } from 'react-router-dom';
import { FaClipboardList, FaSignOutAlt, FaHome } from 'react-icons/fa';
import './BookStore.css'; // Create this CSS file for custom styles
import Footer from '../Components/Footer';

// Dark Academia Navbar for Book Store
function BookStoreNavbar() {
  const navigate = useNavigate();
  const user = JSON.parse(localStorage.getItem('user'));

  const handleLogout = () => {
    localStorage.removeItem('user');
    alert('Logged out successfully!');
    navigate('/');
  };

  return (
    <nav className="bg-gradient-to-r from-amber-900 to-orange-800 shadow-lg">
      <div className="container mx-auto px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <FaClipboardList className="text-amber-200 text-2xl" />
            <h1 className="text-2xl font-bold text-amber-100 font-serif-primary">My Orders</h1>
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={() => navigate('/books')}
              className="flex items-center space-x-2 px-4 py-2 text-amber-100 hover:text-white hover:bg-amber-700 rounded-lg transition-all font-sans"
            >
              <FaHome className="text-sm" />
              <span>Browse Books</span>
            </button>
            {user ? (
              <button
                onClick={handleLogout}
                className="flex items-center space-x-2 bg-red-100 text-red-700 px-4 py-2 rounded-lg text-sm font-semibold hover:bg-red-200 transition-colors"
              >
                <FaSignOutAlt />
                <span>Logout</span>
              </button>
            ) : (
              <button
                onClick={() => navigate('/login')}
                className="flex items-center space-x-2 bg-green-100 text-green-700 px-4 py-2 rounded-lg text-sm font-semibold hover:bg-green-200 transition-colors"
              >
                <span>Login</span>
              </button>
            )}
          </div>
        </div>
      </div>
    </nav>
  );
}

// Book List Page
function BookList() {
  const [books, setBooks] = useState([]);
  const navigate = useNavigate();

  useEffect(() => {
    axios.get('https://booknest-backend-55yh.onrender.com/books')
      .then(res => setBooks(res.data))
      .catch(err => console.error('Error fetching books:', err));
  }, []);

  return (
    <div className="container mt-4">
      <h1 className="text-center mb-4">Welcome to the Book Store</h1>
      <div className="row">
        {books.map(book => (
          <div className="col-md-3 mb-4" key={book._id}>
            <Card className="h-100 bookstore-card">
              <Card.Img variant="top" src={`/${book.coverImage}`} alt={book.title} style={{ height: '250px', objectFit: 'cover' }} />
              <Card.Body>
                <Card.Title>{book.title}</Card.Title>
                <Card.Text>
                  <strong>Author:</strong> {book.author}<br />
                  <strong>Price:</strong> ${book.price}
                </Card.Text>
                <Button variant="primary" onClick={() => navigate(`/book/${book._id}`)}>View Details</Button>
              </Card.Body>
            </Card>
          </div>
        ))}
      </div>
    </div>
  );
}

// Book Details Page
function BookDetails({ bookId }) {
  const [book, setBook] = useState(null);
  const [orderSuccess, setOrderSuccess] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    axios.get(`https://booknest-backend-55yh.onrender.com/books/${bookId}`)
      .then(res => setBook(res.data))
      .catch(err => console.error('Error fetching book:', err));
  }, [bookId]);

  const handleOrder = () => {
    const user = JSON.parse(localStorage.getItem('user'));
    if (!user) {
      navigate('/login');
      return;
    }
    axios.post('https://booknest-backend-55yh.onrender.com/order', {
      userId: user.id,
      bookId: book._id
    })
      .then(() => setOrderSuccess(true))
      .catch(err => alert('Order failed!'));
  };

  if (!book) return <div className="container mt-5">Loading...</div>;

  return (
    <div className="container mt-5">
      <div className="row">
        <div className="col-md-4">
          <img src={`/${book.coverImage}`} alt={book.title} className="img-fluid" />
        </div>
        <div className="col-md-8">
          <h2>{book.title}</h2>
          <p><strong>Author:</strong> {book.author}</p>
          <p><strong>Description:</strong> {book.description}</p>
          <p><strong>Price:</strong> ${book.price}</p>
          <Button variant="success" onClick={handleOrder}>Order Now</Button>
          {orderSuccess && <div className="alert alert-success mt-3">Order placed successfully!</div>}
        </div>
      </div>
    </div>
  );
}

// My Orders Page
function MyOrders() {
  const [orders, setOrders] = useState([]);
  const [books, setBooks] = useState({});
  const navigate = useNavigate();

  useEffect(() => {
    const user = JSON.parse(localStorage.getItem('user'));
    if (!user) {
      navigate('/login');
      return;
    }
    axios.get(`https://booknest-backend-55yh.onrender.com/getorders/${user.id}`)
      .then(res => {
        setOrders(res.data);
        // Fetch all books for mapping
        axios.get('https://booknest-backend-55yh.onrender.com/books')
          .then(bookRes => {
            const bookMap = {};
            bookRes.data.forEach(b => { bookMap[b._id] = b; });
            setBooks(bookMap);
          });
      })
      .catch(err => console.error('Error fetching orders:', err));
  }, [navigate]);

  return (
    <div>
      <BookStoreNavbar />
      <div className="container mt-4">
        <h1 className="text-center mb-4">My Orders</h1>
        {orders.length === 0 ? (
          <div className="text-center">No orders found.</div>
        ) : (
          <div className="row">
            {orders.map(order => {
              const book = books[order.bookId];
              return (
                <div className="col-md-6 mb-4" key={order._id}>
                  <Card className="bookstore-card">
                    <div className="d-flex align-items-center">
                      <img
                        src={book ? `/${book.coverImage}` : '/default_cover.jpg'}
                        alt={book ? book.title : 'Book'}
                        style={{ height: '100px', width: '80px', objectFit: 'cover', marginRight: '20px' }}
                      />
                      <div>
                        <h5>{book ? book.title : 'Book'}</h5>
                        <p><strong>Order ID:</strong> {order._id.slice(0, 10)}</p>
                        <p><strong>Price:</strong> ${book ? book.price : '--'}</p>
                        <p><strong>Status:</strong> {order.status || 'Processing'}</p>
                        <p><strong>Order Date:</strong> {order.orderDate ? new Date(order.orderDate).toLocaleDateString() : '--'}</p>
                      </div>
                    </div>
                  </Card>
                </div>
              );
            })}
          </div>
        )}
      </div>
      <Footer />
    </div>
  );
}

// Routing Setup
import { BrowserRouter as Router, Routes, Route, useParams } from 'react-router-dom';

function BookDetailsWrapper() {
  const { id } = useParams();
  return <BookDetails bookId={id} />;
}

function App() {
  return (
    <Router>
      <BookStoreNavbar />
      <Routes>
        <Route path="/" element={<BookList />} />
        <Route path="/book/:id" element={<BookDetailsWrapper />} />
        <Route path="/myorders" element={<MyOrders />} />
        {/* Add login/register routes as needed */}
      </Routes>
      <Footer />
    </Router>
  );
}

export default App;

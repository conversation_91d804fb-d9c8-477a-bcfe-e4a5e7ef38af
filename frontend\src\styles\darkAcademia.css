/* Dark Academia Design System */
@import url('https://fonts.googleapis.com/css2?family=Crimson+Text:ital,wght@0,400;0,600;1,400&family=Playfair+Display:wght@400;700;900&family=Source+Sans+Pro:wght@300;400;600&display=swap');

/* Dark Academia Theme for BookNest */
:root {
  --da-bg: #23201a;
  --da-bg-alt: #2d2922;
  --da-text: #e6e1d3;
  --da-accent: #bfa181;
  --da-accent-dark: #8c6a4f;
  --da-border: #3a362c;
  --da-card: #28251f;
  --da-link: #bfa181;
  --da-link-hover: #e6e1d3;
  --da-shadow: 0 2px 8px rgba(44, 38, 28, 0.2);
  --da-font: 'Merriweather', 'Georgia', serif;

  /* Primary Colors */
  --dark-academia-primary: #2c1810;
  --dark-academia-secondary: #8b4513;
  --dark-academia-accent: #d2b48c;
  
  /* Text Colors */
  --text-primary: #1a1a1a;
  --text-secondary: #4a4a4a;
  --text-accent: #8b4513;
  
  /* Background Colors */
  --bg-primary: #f5f5dc;
  --bg-secondary: #faf0e6;
  --bg-accent: #fff8dc;
  
  /* Accent Colors */
  --gold: #ffd700;
  --burgundy: #800020;
  --forest-green: #355e3b;
}

body {
  background: var(--da-bg);
  color: var(--da-text);
  font-family: var(--da-font);
  min-height: 100vh;
}

a, .btn-link {
  color: var(--da-link);
  text-decoration: none;
}
a:hover, .btn-link:hover {
  color: var(--da-link-hover);
  text-decoration: underline;
}

.navbar, .navbar-dark {
  background: var(--da-bg-alt) !important;
  border-bottom: 1px solid var(--da-border);
}

.navbar-brand img {
  height: 40px;
  margin-right: 10px;
}

.card {
  background: var(--da-card);
  color: var(--da-text);
  border: 1px solid var(--da-border);
  box-shadow: var(--da-shadow);
}

.btn-da {
  background: var(--da-accent);
  color: var(--da-bg);
  border: none;
  font-weight: bold;
  transition: background 0.2s;
}
.btn-da:hover {
  background: var(--da-accent-dark);
  color: var(--da-text);
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--da-font);
  color: var(--da-accent);
}

input, select, textarea {
  background: var(--da-bg-alt);
  color: var(--da-text);
  border: 1px solid var(--da-border);
}

::-webkit-scrollbar {
  width: 8px;
  background: var(--da-bg-alt);
}
::-webkit-scrollbar-thumb {
  background: var(--da-accent-dark);
  border-radius: 4px;
}

/* Typography Classes */
.font-serif-primary { 
  font-family: 'Playfair Display', serif; 
}

.font-serif-secondary { 
  font-family: 'Crimson Text', serif; 
}

.font-sans { 
  font-family: 'Source Sans Pro', sans-serif; 
}

/* Hero Banner Styles */
.hero-banner {
  background: linear-gradient(135deg, #2c1810 0%, #8b4513 50%, #d2b48c 100%);
  min-height: 60vh;
  position: relative;
  overflow: hidden;
}

.hero-banner::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="books" patternUnits="userSpaceOnUse" width="20" height="20"><rect width="20" height="20" fill="none"/><path d="M2 2h16v16H2z" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23books)"/></svg>');
  opacity: 0.3;
}

.hero-content {
  color: #f5f5dc;
  font-family: 'Crimson Text', 'Georgia', serif;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.7);
  position: relative;
  z-index: 1;
}

/* Featured Books Section */
.featured-books {
  background: linear-gradient(to bottom, #faf0e6, #fff8dc);
}

.book-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  overflow: hidden;
  position: relative;
}

.book-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15);
}

.book-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 0%, rgba(139, 69, 19, 0.1) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.book-card:hover::before {
  opacity: 1;
}

.book-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.book-card:hover .book-image {
  transform: scale(1.05);
}

/* Rating Stars */
.rating-stars {
  display: flex;
  align-items: center;
  gap: 2px;
}

.star {
  color: #ffd700;
  font-size: 16px;
}

.star.empty {
  color: #e5e5e5;
}

/* Categories Navigation */
.categories-nav {
  background: linear-gradient(to right, #8b4513, #d2b48c);
  padding: 1rem 0;
}

.category-btn {
  background: rgba(245, 245, 220, 0.9);
  color: #8b4513;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  font-weight: 600;
  transition: all 0.3s ease;
  white-space: nowrap;
  font-family: 'Source Sans Pro', sans-serif;
}

.category-btn:hover {
  background: rgba(245, 245, 220, 1);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Floating Action Buttons */
.floating-actions {
  position: fixed;
  top: 100px;
  right: 24px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.fab {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.fab:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.4);
}

.cart-fab {
  background: linear-gradient(135deg, #d97706, #ea580c);
}

.orders-fab {
  background: linear-gradient(135deg, #2563eb, #4f46e5);
}

.fab-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: #ef4444;
  color: white;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
}

/* Header Cart Section */
.header-cart-section {
  display: flex;
  align-items: center;
  gap: 16px;
}

.cart-preview, .orders-link {
  padding: 8px 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: 'Source Sans Pro', sans-serif;
}

.cart-preview {
  background: rgba(245, 245, 220, 0.8);
  border: 1px solid #d2b48c;
}

.cart-preview:hover {
  background: rgba(245, 245, 220, 1);
  transform: translateY(-2px);
}

.orders-link {
  background: rgba(219, 234, 254, 0.8);
  border: 1px solid #93c5fd;
}

.orders-link:hover {
  background: rgba(219, 234, 254, 1);
  transform: translateY(-2px);
}

/* Compact Login Styles */
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #faf0e6 0%, #fff8dc 50%, #ffe4e1 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
}

.login-card {
  width: 100%;
  max-width: 400px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(210, 180, 140, 0.3);
  padding: 2rem;
}

.login-header {
  text-align: center;
  margin-bottom: 1.5rem;
}

.login-icon {
  width: 64px;
  height: 64px;
  background: linear-gradient(135deg, #d97706, #ea580c);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
}

.demo-credentials {
  background: rgba(245, 245, 220, 0.8);
  border: 1px solid #d2b48c;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 1rem;
  font-size: 14px;
}

.demo-credential-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.demo-credential-code {
  background: rgba(210, 180, 140, 0.3);
  padding: 2px 8px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.login-input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #d2b48c;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.9);
  color: #8b4513;
  font-family: 'Source Sans Pro', sans-serif;
  transition: all 0.3s ease;
}

.login-input:focus {
  outline: none;
  border-color: #8b4513;
  box-shadow: 0 0 0 3px rgba(139, 69, 19, 0.1);
}

.login-input::placeholder {
  color: rgba(139, 69, 19, 0.6);
}

.login-button {
  width: 100%;
  background: linear-gradient(135deg, #d97706, #ea580c);
  color: white;
  border: none;
  padding: 12px;
  border-radius: 8px;
  font-weight: 600;
  font-family: 'Source Sans Pro', sans-serif;
  cursor: pointer;
  transition: all 0.3s ease;
}

.login-button:hover {
  background: linear-gradient(135deg, #c2410c, #dc2626);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-banner {
    min-height: 40vh;
    padding: 2rem 1rem;
  }
  
  .floating-actions {
    top: auto;
    bottom: 20px;
    right: 20px;
    flex-direction: row;
  }
  
  .fab {
    width: 50px;
    height: 50px;
    font-size: 20px;
  }
  
  .header-cart-section {
    flex-direction: column;
    gap: 8px;
  }
  
  .categories-nav {
    padding: 0.5rem;
  }
  
  .category-btn {
    padding: 0.5rem 1rem;
    font-size: 14px;
  }
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.6s ease-in;
}

.slide-up {
  animation: slideUp 0.6s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    opacity: 0;
    transform: translateY(30px);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}

/* Loading States */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* Enhanced Responsive Design */
@media (max-width: 480px) {
  .hero-banner h1 {
    font-size: 2rem;
  }

  .featured-books-grid {
    grid-template-columns: 1fr;
  }

  .categories-nav {
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .categories-nav::-webkit-scrollbar {
    display: none;
  }

  .fab {
    width: 45px;
    height: 45px;
  }

  .login-card {
    margin: 0.5rem;
    padding: 1rem;
  }
}

/* Enhanced Hover Effects */
.book-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 40px rgba(44, 24, 16, 0.3);
}

.category-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(139, 69, 19, 0.2);
}

.fab:hover {
  transform: translateY(-3px) scale(1.1);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

/* Micro-interactions */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.fab-badge {
  animation: pulse 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-10px); }
  60% { transform: translateY(-5px); }
}

.hero-cta:hover {
  animation: bounce 1s;
}

/* Focus States for Accessibility */
.login-input:focus,
.search-input:focus {
  outline: none;
  ring: 2px solid var(--accent-color);
  border-color: var(--accent-color);
}

button:focus {
  outline: 2px solid var(--accent-color);
  outline-offset: 2px;
}

/* Print Styles */
@media print {
  .floating-actions,
  .hero-banner,
  .categories-nav {
    display: none;
  }

  .book-card {
    break-inside: avoid;
    box-shadow: none;
    border: 1px solid #ccc;
  }
}

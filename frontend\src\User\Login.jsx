import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { <PERSON>aB<PERSON>, <PERSON>a<PERSON>ye, FaEyeSlash, FaUser, FaLock } from 'react-icons/fa';
import axios from 'axios';

const Login = () => {
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('demo123');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [currentUser, setCurrentUser] = useState(null);
  const navigate = useNavigate();

  // Check if user is already logged in
  useEffect(() => {
    const user = localStorage.getItem('user');
    if (user) {
      setIsLoggedIn(true);
      setCurrentUser(JSON.parse(user));
    }
  }, []);

  axios.defaults.withCredentials = true;



  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    // Basic validation
    if (!email.trim() || !password.trim()) {
      setError('Please fill in all fields');
      setLoading(false);
      return;
    }

    try {
      const payload = { email: email.trim(), password };
      const response = await axios.post("https://booknest-backend-55yh.onrender.com/login", payload);

      if (response.data.success) {
        localStorage.setItem('user', JSON.stringify(response.data.user));
        alert('Login successful!');
        navigate('/welcome');
      } else {
        setError(response.data.message || 'Invalid credentials');
      }
    } catch (err) {
      console.error('Login error:', err);
      setError('Login failed. Please check your connection and try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleSignup = (e) => {
    e.preventDefault();
    navigate("/signup");
  };

  return (
    <div className="login-container">
      <div className="login-card">
        {/* Compact Header */}
        <div className="login-header">
          <div className="flex justify-between items-start w-full">
            <div className="flex-1">
              <div className="login-icon">
                <FaBook className="text-white text-2xl" />
              </div>
              <h2 className="text-2xl font-bold text-amber-900 font-serif-primary">
                {isLoggedIn ? `Welcome, ${currentUser?.name || 'User'}` : 'Welcome Back'}
              </h2>
              <p className="text-amber-700 text-sm font-sans">
                {isLoggedIn ? 'You are currently logged in' : 'Sign in to your account'}
              </p>
            </div>

          </div>
        </div>

        {/* Demo Credentials Display */}
        <div className="demo-credentials">
          <p className="text-xs text-amber-800 font-semibold mb-2">Demo Credentials:</p>
          <div className="text-xs text-amber-700 space-y-1">
            <div className="demo-credential-row">
              <span>Email:</span>
              <code className="demo-credential-code"><EMAIL></code>
            </div>
            <div className="demo-credential-row">
              <span>Password:</span>
              <code className="demo-credential-code">demo123</code>
            </div>
          </div>
        </div>

        {/* Compact Form */}
        <form className="space-y-4" onSubmit={handleSubmit}>
          <div className="relative">
            <FaUser className="absolute left-4 top-1/2 transform -translate-y-1/2 text-amber-600" />
            <input
              type="email"
              placeholder="Email address"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="login-input pl-12"
              required
            />
          </div>

          <div className="relative">
            <FaLock className="absolute left-4 top-1/2 transform -translate-y-1/2 text-amber-600" />
            <input
              type={showPassword ? "text" : "password"}
              placeholder="Password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="login-input pl-12 pr-12"
              required
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute right-4 top-1/2 transform -translate-y-1/2 text-amber-600 hover:text-amber-800"
            >
              {showPassword ? <FaEyeSlash /> : <FaEye />}
            </button>
          </div>

          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg text-sm">
              {error}
            </div>
          )}

          <button
            type="submit"
            disabled={loading}
            className="login-button"
          >
            {loading ? 'Signing In...' : 'Sign In'}
          </button>
        </form>

        {/* Quick Actions */}
        <div className="mt-6 flex justify-between text-xs">
          <button
            onClick={() => navigate('/')}
            className="text-amber-600 hover:text-amber-800 transition-colors"
          >
            ← Back to Home
          </button>
          <button
            onClick={handleSignup}
            className="text-blue-600 hover:text-blue-800 transition-colors"
          >
            Create Account
          </button>
        </div>

        {/* Alternative Login Options */}
        <div className="mt-6 pt-4 border-t border-amber-200">
          <p className="text-center text-xs text-amber-700 mb-3">Or continue as:</p>
          <div className="flex space-x-2">
            <button
              onClick={() => navigate('/slogin')}
              className="flex-1 bg-green-100 text-green-700 py-2 px-3 rounded-lg text-xs font-semibold
                        hover:bg-green-200 transition-colors"
            >
              Seller
            </button>
            <button
              onClick={() => navigate('/alogin')}
              className="flex-1 bg-purple-100 text-purple-700 py-2 px-3 rounded-lg text-xs font-semibold
                        hover:bg-purple-200 transition-colors"
            >
              Admin
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;

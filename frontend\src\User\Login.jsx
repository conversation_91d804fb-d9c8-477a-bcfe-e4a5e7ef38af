import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { <PERSON>a<PERSON><PERSON>, <PERSON>a<PERSON>ye, FaEyeSlash, FaUser, FaLock } from 'react-icons/fa';
import axios from 'axios';

// Set your backend login endpoint here
const LOGIN_ENDPOINT = 'https://booknest-backend-55yh.onrender.com/api/auth/login';

const Login = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [userType, setUserType] = useState('user');
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [currentUser, setCurrentUser] = useState(null);
  const navigate = useNavigate();

  // Check if user is already logged in
  React.useEffect(() => {
    const user = localStorage.getItem('user');
    if (user) {
      setIsLoggedIn(true);
      setCurrentUser(JSON.parse(user));
    }
  }, []);

  axios.defaults.withCredentials = true;

  const handleLogin = async (e) => {
    e.preventDefault();
    setError(null);
    setLoading(true);

    // Basic validation
    if (!email.trim() || !password.trim()) {
      setError('Please fill in all fields');
      setLoading(false);
      return;
    }

    try {
      const res = await fetch(LOGIN_ENDPOINT, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email, password, userType }),
      });
      const data = await res.json();
      if (res.ok && data.token) {
        localStorage.setItem('token', data.token);
        localStorage.setItem('user', JSON.stringify(data.user));
        setIsLoggedIn(true);
        setCurrentUser(data.user);
        navigate('/');
      } else {
        console.error('Login failed response:', data);
        setError(data.message || 'Login failed');
      }
    } catch (err) {
      console.error('Login error:', err);
      setError('Login failed. Please check your connection and try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleDemoLogin = () => {
    setEmail('<EMAIL>');
    setPassword('demopassword');
    setUserType('user');
  };

  const handleSignup = (e) => {
    e.preventDefault();
    navigate("/signup");
  };

  return (
    <div className="container py-5 d-flex justify-content-center align-items-center" style={{minHeight: '80vh'}}>
      <div className="card p-4 shadow" style={{background: 'var(--da-card)', minWidth: 350}}>
        <h2 className="mb-4 text-center" style={{fontFamily: 'Playfair Display, serif'}}>Login</h2>
        <form onSubmit={handleLogin}>
          <div className="mb-3">
            <label className="form-label">Email</label>
            <input type="email" className="form-control" value={email} onChange={e => setEmail(e.target.value)} required />
          </div>
          <div className="mb-3">
            <label className="form-label">Password</label>
            <input type="password" className="form-control" value={password} onChange={e => setPassword(e.target.value)} required />
          </div>
          <div className="mb-3">
            <label className="form-label">Login as</label>
            <select className="form-select" value={userType} onChange={e => setUserType(e.target.value)}>
              <option value="user">User</option>
              <option value="seller">Seller</option>
              <option value="admin">Admin</option>
            </select>
          </div>
          {error && <div className="alert alert-danger">{error}</div>}
          <button type="submit" className="btn btn-da w-100 mb-2">Login</button>
          <button type="button" className="btn btn-outline-secondary w-100" onClick={handleDemoLogin}>Demo Login</button>
        </form>
        <div className="mt-6 flex justify-between text-xs">
          <button
            onClick={() => navigate('/')}
            className="text-amber-600 hover:text-amber-800 transition-colors"
          >
            ← Back to Home
          </button>
          <button
            onClick={handleSignup}
            className="text-blue-600 hover:text-blue-800 transition-colors"
          >
            Create Account
          </button>
        </div>
      </div>
    </div>
  );
};

export default Login;
